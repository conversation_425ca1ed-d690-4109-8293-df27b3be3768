import UtilityService from './UtilityService';
import APISauceService from './APISauceService';

class RestService {
  static postJSON(url, data = {}, params = [], isLoginIn=false) {
    return APISauceService.postJSON(url, data, params,isLoginIn);
  }

  static postJSONWithoutTimeout(url, data = {}, params = []) {
    return APISauceService.postJSONWithoutTimeout(url, data, params);
  }

  static postWithHeaders(url, data = {}, params = [], headers = {}) {
    return APISauceService.postWithHeaders(url, data, params, headers);
  }

  static getJSON(url, params = []) {
    return APISauceService.getJSON(url, params);
  }

  static get(url, headers, responseType, params = {}) {
    return APISauceService.get(url, headers, responseType, params);
  }

  static putJSON(url, data = {}, params = []) {
    return APISauceService.putJSON(url, data, params);
  }

  static deleteJSON(url, params = []) {
    return APISauceService.deleteJSON(url, params);
  }

  static postMultipart(url, data = {}, config = [], params = {}) {
    return APISauceService.postMultipart(url, data, config, params);
  }

  static constructURL(url, pathVariablesJSON, requestParams) {
    if (!UtilityService.checkEmpty(url) && !UtilityService.checkEmpty(pathVariablesJSON)) {
      Object.keys(pathVariablesJSON).map((key) => {
        url = url.replace(`{${key}}`, pathVariablesJSON[key]);
        return null;
      });
    }
    const params = [];
    if (!UtilityService.checkEmpty(url) && !UtilityService.checkEmpty(requestParams)) {
      Object.keys(requestParams).map((key) => {
        params.push(`${key}=${requestParams[key]}`);
        return null;
      });
      url += `?${params.join('&')}`;
    }
    return url;
  }

  static addRequestTransform() {
    APISauceService.addRequestTransform();
  }
}

export default RestService;
